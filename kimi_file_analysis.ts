import { Args } from '@/runtime';
import axios from "axios";
import FormData from 'form-data';
import { Input, Output } from "@/typings/kimi_file_analysis/kimi_file_analysis";



/**
 * Kimi AI 文件分析插件
 * 支持上传文件到Kimi并进行智能分析
 * 支持多种文件格式：PDF、Word、Excel、PPT、TXT、图片等
 *
 * 输入参数示例：
 *
 * 1. 通过URL分析PDF文档：
 * {
 *   "file_url": "https://example.com/document.pdf",
 *   "analysis_prompt": "请总结这个PDF文档的主要内容，并提取其中的关键信息点和重要数据"
 * }
 *
 * 2. 分析上传的Excel文件：
 * {
 *   "file_content": "UEsDBBQAAAAIAA...", // base64编码的Excel文件内容
 *   "file_name": "sales_data.xlsx",
 *   "analysis_prompt": "请分析这个Excel文件中的销售数据，包括：1. 总销售额 2. 各产品销售情况 3. 销售趋势分析 4. 提出改进建议"
 * }
 *
 * 3. 分析代码文件：
 * {
 *   "file_content": "ZnVuY3Rpb24gZmlib25hY2NpKG4pIHsKICByZXR1cm4gbiA8PSAxID8gbiA6IGZpYm9uYWNjaShuLTEpICsgZmlib25hY2NpKG4tMik7Cn0=", // base64编码的代码文件
 *   "file_name": "fibonacci.js",
 *   "analysis_prompt": "请分析这段代码的功能、性能问题，并提供优化建议"
 * }
 *
 * 4. 分析图片文件：
 * {
 *   "file_url": "https://example.com/chart.png",
 *   "file_name": "chart.png",
 *   "analysis_prompt": "请分析这张图片中的图表数据，描述趋势和关键发现"
 * }
 *
 * 5. 分析Word文档：
 * {
 *   "file_content": "UEsDBBQAAAAIAA...", // base64编码的Word文档
 *   "file_name": "report.docx",
 *   "analysis_prompt": "请提取文档中的核心观点，并生成一份执行摘要"
 * }
 *
 * 注意：
 * - file_url 和 file_content 二选一，不能同时为空
 * - file_content 需要是base64编码的字符串
 * - analysis_prompt 是必填项，描述你希望如何分析文件
 * - 支持的文件格式：PDF、Word、Excel、PPT、TXT、图片等
 */
export async function handler({ input, logger }: Args<Input>): Promise<Output> {
  try {
    logger.info("开始处理Kimi文件分析请求", {
      hasFileUrl: !!input.file_url,
      hasFileContent: !!input.file_content,
      fileName: input.file_name,
      analysisPrompt: input.analysis_prompt
    });

    // 验证输入参数
    if (!input.file_url && !input.file_content) {
      logger.error("未提供文件URL或文件内容");
      return {
        message: "请提供文件URL或文件内容",
        file_id: null,
        analysis_result: null,
        status: false
      } as Output;
    }

    if (!input.analysis_prompt || input.analysis_prompt.trim() === '') {
      logger.error("未提供分析提示词");
      return {
        message: "请提供分析提示词，说明您希望如何分析这个文件",
        file_id: null,
        analysis_result: null,
        status: false
      } as Output;
    }

    // 第一步：上传文件到Kimi
    const uploadResult = await uploadFileToKimi(input, logger);
    if (!uploadResult.success) {
      return {
        message: uploadResult.error || "文件上传失败",
        file_id: null,
        analysis_result: null,
        status: false
      } as Output;
    }

    logger.info("文件上传成功", { fileId: uploadResult.fileId });

    // 第二步：等待文件处理完成
    const processResult = await waitForFileProcessing(uploadResult.fileId!, logger);
    if (!processResult.success) {
      return {
        message: processResult.error || "文件处理失败",
        file_id: uploadResult.fileId,
        analysis_result: null,
        status: false
      } as Output;
    }

    logger.info("文件处理完成，开始分析");

    // 第三步：使用文件进行对话分析
    const analysisResult = await analyzeFileWithKimi(uploadResult.fileId!, input.analysis_prompt, logger);
    if (!analysisResult.success) {
      return {
        message: analysisResult.error || "文件分析失败",
        file_id: uploadResult.fileId,
        analysis_result: null,
        status: false
      } as Output;
    }

    logger.info("文件分析完成", {
      analysisLength: analysisResult.data?.length || 0
    });

    return {
      message: "文件分析成功",
      file_id: uploadResult.fileId,
      analysis_result: analysisResult.data,
      status: true
    } as Output;

  } catch (error) {
    logger.error("处理Kimi文件分析请求时发生异常", error);
    return {
      message: error instanceof Error ? error.message : '未知错误',
      file_id: null,
      analysis_result: null,
      status: false
    } as Output;
  }
}

/**
 * 上传文件到Kimi
 * @param input 输入参数
 * @param logger 日志记录器
 * @returns 上传结果
 */
async function uploadFileToKimi(input: Input, logger: any): Promise<{success: boolean, fileId?: string, error?: string}> {
  try {
    const apiKey = "sk-woBMElIQkhFn9xCmZQKeAvln64Y2aIWenKN2Tm6qPOKzjwhr";
    const baseURL = "https://api.moonshot.cn/v1";

    let fileBuffer: Buffer;
    let fileName: string;

    // 根据输入类型获取文件数据
    if (input.file_url) {
      logger.info("从URL下载文件", { url: input.file_url });
      const response = await axios.get(input.file_url, { responseType: 'arraybuffer' });
      fileBuffer = Buffer.from(response.data);
      fileName = input.file_name || extractFileNameFromUrl(input.file_url);
    } else if (input.file_content) {
      logger.info("使用提供的文件内容");
      // 假设file_content是base64编码的字符串
      fileBuffer = Buffer.from(input.file_content, 'base64');
      fileName = input.file_name || 'uploaded_file.txt';
    } else {
      return { success: false, error: "未提供有效的文件数据" };
    }

    logger.info("准备上传文件", { 
      fileName: fileName, 
      fileSize: fileBuffer.length 
    });

    // 创建FormData
    const formData = new FormData();
    formData.append('file', fileBuffer, fileName);
    formData.append('purpose', 'file-extract');

    // 上传文件
    const response = await axios.post(`${baseURL}/files`, formData, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        ...formData.getHeaders()
      },
      timeout: 120000 // 2分钟超时
    });

    logger.info("文件上传API调用成功", {
      status: response.status,
      fileId: response.data?.id,
      fileName: response.data?.filename
    });

    if (!response.data || !response.data.id) {
      logger.error("文件上传返回数据格式异常", response.data);
      return { success: false, error: "文件上传返回数据格式异常" };
    }

    return {
      success: true,
      fileId: response.data.id
    };

  } catch (error) {
    logger.error("上传文件到Kimi时发生异常", error);
    
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const errorData = error.response?.data;
      
      logger.error("文件上传API错误详情", {
        status: status,
        errorData: errorData,
        message: error.message
      });
      
      switch (status) {
        case 413:
          return { success: false, error: "文件大小超出限制" };
        case 415:
          return { success: false, error: "不支持的文件格式" };
        default:
          return { 
            success: false, 
            error: errorData?.error?.message || error.message || "文件上传失败" 
          };
      }
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : "文件上传失败"
    };
  }
}

/**
 * 等待文件处理完成
 * @param fileId 文件ID
 * @param logger 日志记录器
 * @returns 处理结果
 */
async function waitForFileProcessing(fileId: string, logger: any): Promise<{success: boolean, error?: string}> {
  try {
    const apiKey = "sk-woBMElIQkhFn9xCmZQKeAvln64Y2aIWenKN2Tm6qPOKzjwhr";
    const baseURL = "https://api.moonshot.cn/v1";
    
    const maxAttempts = 30; // 最多等待30次，每次2秒，总共1分钟
    const delayMs = 2000; // 2秒间隔

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      logger.info(`检查文件处理状态 (${attempt}/${maxAttempts})`, { fileId });

      const response = await axios.get(`${baseURL}/files/${fileId}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });

      const fileStatus = response.data?.status;
      logger.info("文件状态检查结果", { 
        fileId, 
        status: fileStatus, 
        attempt 
      });

      if (fileStatus === 'processed') {
        logger.info("文件处理完成", { fileId });
        return { success: true };
      } else if (fileStatus === 'error') {
        logger.error("文件处理失败", { fileId });
        return { success: false, error: "文件处理失败" };
      }

      // 等待后继续检查
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    logger.error("文件处理超时", { fileId });
    return { success: false, error: "文件处理超时，请稍后重试" };

  } catch (error) {
    logger.error("检查文件处理状态时发生异常", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "检查文件状态失败"
    };
  }
}

/**
 * 使用文件进行对话分析
 * @param fileId 文件ID
 * @param analysisPrompt 分析提示词
 * @param logger 日志记录器
 * @returns 分析结果
 */
async function analyzeFileWithKimi(fileId: string, analysisPrompt: string, logger: any): Promise<{success: boolean, data?: string, error?: string}> {
  try {
    const apiKey = "sk-woBMElIQkhFn9xCmZQKeAvln64Y2aIWenKN2Tm6qPOKzjwhr";
    const baseURL = "https://api.moonshot.cn/v1";

    const requestData = {
      model: "moonshot-v1-32k", // 使用32k模型以支持更长的文件内容
      messages: [
        {
          role: "system",
          content: "你是一个专业的文件分析助手，能够深入理解和分析各种类型的文档内容。"
        },
        {
          role: "user",
          content: [
            {
              type: "file",
              file_url: {
                url: `file://${fileId}`
              }
            },
            {
              type: "text",
              text: analysisPrompt
            }
          ]
        }
      ],
      temperature: 0.3
    };

    logger.info("开始文件分析对话", {
      fileId,
      model: requestData.model,
      promptLength: analysisPrompt.length
    });

    const response = await axios.post(`${baseURL}/chat/completions`, requestData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 120000 // 2分钟超时
    });

    logger.info("文件分析API调用成功", {
      status: response.status,
      hasChoices: response.data?.choices?.length > 0
    });

    if (!response.data || !response.data.choices || response.data.choices.length === 0) {
      logger.error("文件分析返回数据格式异常", response.data);
      return { success: false, error: "分析返回数据格式异常" };
    }

    const analysisContent = response.data.choices[0].message.content;
    return {
      success: true,
      data: analysisContent
    };

  } catch (error) {
    logger.error("文件分析时发生异常", error);
    
    if (axios.isAxiosError(error)) {
      const errorData = error.response?.data;
      return { 
        success: false, 
        error: errorData?.error?.message || error.message || "文件分析失败" 
      };
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : "文件分析失败"
    };
  }
}

/**
 * 从URL中提取文件名
 * @param url 文件URL
 * @returns 文件名
 */
function extractFileNameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const fileName = pathname.split('/').pop() || 'downloaded_file';
    return fileName;
  } catch {
    return 'downloaded_file';
  }
}
