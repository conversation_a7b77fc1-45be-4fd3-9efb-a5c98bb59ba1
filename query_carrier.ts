import { Args } from '@/runtime';
import { Input, Output } from "@/typings/query_carrier/query_carrier";
import axios from "axios";

export async function handler({ input, logger }: Args<Input>): Promise<Output> {
  try {
    const carrierName = input.name;

    const response = await axios.get('https://res.17track.net/asset/carrier/info/apicarrier.all.json');
    const carriers = response.data;

    logger.info("获取到载体数据", carriers.length);

    if (!carrierName) {
      return {
        message: "请提供载体名称",
        result: [],
        status: false
      } as Output;
    }

    // 根据 carrierName 筛选数据，支持模糊匹配
    const filteredCarriers = carriers.filter((carrier: any) => {
      const name = carrier._name?.toLowerCase() || '';
      const nameZhCn = carrier['_name_zh-cn']?.toLowerCase() || '';
      const nameZhHk = carrier['_name_zh-hk']?.toLowerCase() || '';
      const searchName = carrierName.toLowerCase();

      return name.includes(searchName) ||
             nameZhCn.includes(searchName) ||
             nameZhHk.includes(searchName);
    });

    logger.info(`筛选结果: 找到 ${filteredCarriers.length} 个匹配的载体`);

    if (filteredCarriers.length === 0) {
      return {
        message: `未找到包含 "${carrierName}" 的载体`,
        result: [],
        status: false
      } as Output;
    }

    return {
      message: `找到 ${filteredCarriers.length} 个匹配的载体`,
      result: filteredCarriers,
      status: true
    } as Output;

  } catch (error) {
    logger.error(error);
    return {
      message: error instanceof Error ? error.message : 'Unknown error',
      result: [],
      status: false
    } as Output;
  }
};