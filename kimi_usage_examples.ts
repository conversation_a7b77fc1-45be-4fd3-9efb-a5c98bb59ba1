/**
 * Kimi AI 插件使用示例
 * 展示如何在扣子平台中使用各种Kimi AI功能
 */

// ==================== kimi_chat.ts 使用示例 ====================

/**
 * 示例1: 基础聊天对话
 */
const basicChatExample = {
  message: "你好，请介绍一下你自己",
  system_prompt: "你是一个友善的AI助手，擅长回答各种问题",
  model: "moonshot-v1-8k",
  temperature: 0.3
};

/**
 * 示例2: 多轮对话（带历史记录）
 */
const multiTurnChatExample = {
  message: "那你能帮我做什么呢？",
  conversation_history: [
    {
      role: "user",
      content: "你好，请介绍一下你自己"
    },
    {
      role: "assistant", 
      content: "你好！我是Kimi，一个由Moonshot AI开发的人工智能助手。我可以帮助你回答问题、处理文档、进行翻译、分析代码等多种任务。"
    }
  ],
  model: "moonshot-v1-8k"
};

/**
 * 示例3: 带工具调用的对话 - 使用Kimi官方web_search工具
 */
const toolCallChatExample = {
  message: "请帮我搜索一下人工智能的最新发展趋势",
  tools: [
    {
      type: "function",
      function: {
        name: "web_search",
        description: "用于信息检索的网络搜索",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "要搜索的内容"
            },
            classes: {
              type: "array",
              description: "要关注的搜索领域。如果未指定，则默认为 'all'。",
              items: {
                type: "string",
                enum: [
                  "all",
                  "academic",
                  "social",
                  "library",
                  "finance",
                  "code",
                  "ecommerce",
                  "medical"
                ]
              }
            }
          },
          required: ["query"]
        }
      }
    }
  ],
  tool_choice: "auto",
  model: "kimi-latest"
};

/**
 * 示例3.1: 多工具调用对话
 */
const multiToolChatExample = {
  message: "现在几点了？今天北京的天气怎么样？",
  tools: [
    {
      type: "function",
      function: {
        name: "get_current_time",
        description: "获取当前时间，支持指定时区和格式",
        parameters: {
          type: "object",
          properties: {
            timezone: {
              type: "string",
              description: "时区，例如：Asia/Shanghai, America/New_York"
            },
            format: {
              type: "string",
              description: "时间格式：full(完整时间), date(仅日期), time(仅时间)",
              enum: ["full", "date", "time"]
            }
          },
          required: []
        }
      }
    },
    {
      type: "function",
      function: {
        name: "get_weather",
        description: "获取指定地点的天气信息",
        parameters: {
          type: "object",
          properties: {
            location: {
              type: "string",
              description: "地点名称，例如：北京、上海、纽约"
            },
            unit: {
              type: "string",
              description: "温度单位：celsius(摄氏度), fahrenheit(华氏度)",
              enum: ["celsius", "fahrenheit"]
            }
          },
          required: ["location"]
        }
      }
    }
  ],
  tool_choice: "auto",
  model: "kimi-latest"
};

/**
 * 示例4: 数学计算工具调用
 */
const calculatorExample = {
  message: "请帮我计算 (25 + 15) * 3 - 8 的结果",
  tools: [
    {
      type: "function",
      function: {
        name: "calculate",
        description: "执行数学计算，支持基本的四则运算",
        parameters: {
          type: "object",
          properties: {
            expression: {
              type: "string",
              description: "数学表达式，例如：2+3*4, (10-5)/2"
            }
          },
          required: ["expression"]
        }
      }
    }
  ],
  model: "moonshot-v1-8k"
};

// ==================== kimi_file_analysis.ts 使用示例 ====================

/**
 * 示例5: 通过URL分析PDF文档
 */
const pdfAnalysisExample = {
  file_url: "https://example.com/document.pdf",
  analysis_prompt: "请总结这个PDF文档的主要内容，并提取其中的关键信息点和重要数据"
};

/**
 * 示例6: 分析上传的Excel文件
 */
const excelAnalysisExample = {
  file_content: "UEsDBBQAAAAIAA...", // base64编码的Excel文件内容
  file_name: "sales_data.xlsx",
  analysis_prompt: "请分析这个Excel文件中的销售数据，包括：1. 总销售额 2. 各产品销售情况 3. 销售趋势分析 4. 提出改进建议"
};

/**
 * 示例7: 分析代码文件
 */
const codeAnalysisExample = {
  file_content: "ZnVuY3Rpb24gZmlib25hY2NpKG4pIHsKICByZXR1cm4gbiA8PSAxID8gbiA6IGZpYm9uYWNjaShuLTEpICsgZmlib25hY2NpKG4tMik7Cn0=", // base64编码的代码文件
  file_name: "fibonacci.js",
  analysis_prompt: "请分析这段代码的功能、性能问题，并提供优化建议"
};

// ==================== kimi_toolkit.ts 使用示例 ====================

/**
 * 示例8: 文档摘要
 */
const summarizeExample = {
  action: "summarize",
  content: `
    人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，
    并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
    语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，
    应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
    人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、
    也可能超过人的智能。
  `,
  model: "moonshot-v1-8k"
};

/**
 * 示例9: 中英翻译
 */
const translateExample = {
  action: "translate",
  content: "Hello, how are you today? I hope you're having a wonderful day!",
  target_language: "中文",
  model: "moonshot-v1-8k"
};

/**
 * 示例10: 代码分析
 */
const codeAnalysisToolkitExample = {
  action: "code_analysis",
  content: `
    function quickSort(arr) {
      if (arr.length <= 1) return arr;
      
      const pivot = arr[Math.floor(arr.length / 2)];
      const left = arr.filter(x => x < pivot);
      const middle = arr.filter(x => x === pivot);
      const right = arr.filter(x => x > pivot);
      
      return [...quickSort(left), ...middle, ...quickSort(right)];
    }
  `,
  model: "moonshot-v1-8k"
};

/**
 * 示例11: 写作助手
 */
const writingAssistantExample = {
  action: "writing_assistant",
  content: "我今天去了公园，看到了很多花，很漂亮。天气也很好，我很开心。",
  writing_type: "日记",
  model: "moonshot-v1-8k"
};

/**
 * 示例12: 问答提取
 */
const qaExtractionExample = {
  action: "qa_extraction",
  content: `
    什么是机器学习？
    机器学习是人工智能的一个子领域，它使计算机系统能够通过经验自动改进性能，而无需明确编程。
    
    机器学习有哪些主要类型？
    主要有三种类型：监督学习、无监督学习和强化学习。监督学习使用标记数据进行训练，
    无监督学习从未标记数据中发现模式，强化学习通过与环境交互来学习最优行为。
    
    深度学习和机器学习有什么区别？
    深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。
    深度学习在图像识别、自然语言处理等领域表现出色。
  `,
  model: "moonshot-v1-8k"
};

// ==================== 高级使用技巧 ====================

/**
 * 示例13: 自定义系统提示词的专业对话
 */
const professionalChatExample = {
  message: "请帮我分析一下当前市场上主流的前端框架",
  system_prompt: `你是一个资深的前端开发专家，拥有10年以上的开发经验。
    你熟悉各种前端技术栈，包括React、Vue、Angular等主流框架。
    请用专业但易懂的语言回答问题，并提供实用的建议。`,
  model: "moonshot-v1-32k", // 使用更大的上下文模型
  temperature: 0.2 // 降低温度以获得更准确的回答
};

/**
 * 示例14: 长文档处理
 */
const longDocumentExample = {
  action: "summarize",
  content: "这里是一个很长的文档内容...", // 实际使用时可以是几万字的长文档
  model: "moonshot-v1-128k", // 使用128k模型处理长文档
  max_tokens: 4096 // 增加输出长度限制
};

/**
 * 示例15: 创意写作
 */
const creativeWritingExample = {
  action: "writing_assistant",
  content: "写一个关于未来城市的科幻故事开头",
  writing_type: "科幻小说",
  model: "moonshot-v1-8k",
  temperature: 0.8 // 提高温度以增加创造性
};

// ==================== 错误处理示例 ====================

/**
 * 示例16: 处理API调用失败的情况
 */
async function handleApiError() {
  try {
    // 调用插件的代码...
    const result = await kimiChatHandler({
      message: "测试消息",
      model: "moonshot-v1-8k"
    });
    
    if (!result.status) {
      console.error("插件调用失败:", result.message);
      // 处理失败情况的逻辑
    } else {
      console.log("插件调用成功:", result.response?.content);
    }
  } catch (error) {
    console.error("发生异常:", error);
    // 异常处理逻辑
  }
}

/**
 * 使用建议：
 * 
 * 1. 模型选择：
 *    - moonshot-v1-8k: 适合一般对话和短文档处理
 *    - moonshot-v1-32k: 适合中等长度文档和复杂对话
 *    - moonshot-v1-128k: 适合长文档处理和需要大量上下文的任务
 * 
 * 2. 参数调优：
 *    - temperature: 0.1-0.3适合事实性任务，0.7-0.9适合创意性任务
 *    - max_tokens: 根据需要的回复长度调整
 *    - top_p: 一般保持默认值1.0
 * 
 * 3. 工具调用：
 *    - 合理定义工具函数，确保参数描述清晰
 *    - 工具函数应该是幂等的，避免副作用
 *    - 处理工具调用失败的情况
 * 
 * 4. 错误处理：
 *    - 始终检查返回的status字段
 *    - 记录详细的错误日志便于调试
 *    - 为用户提供友好的错误提示
 */

// ==================== 扣子平台工作流程示例 ====================

/**
 * 扣子平台工作流程：智能对话 + 工具调用
 *
 * 步骤1: 用户发送消息给kimi_chat插件
 * 步骤2: kimi_chat插件检测到需要工具调用，返回tool_calls信息
 * 步骤3: 根据tool_calls信息调用kimi_tools插件执行具体工具
 * 步骤4: 将工具执行结果返回给kimi_chat插件继续对话
 */

/**
 * 示例：扣子平台完整工作流程
 */
const cozeWorkflowExample = {
  // 第一步：用户消息 -> kimi_chat插件
  step1_user_input: {
    message: "请帮我搜索一下最新的AI技术发展",
    tools: [
      {
        type: "function",
        function: {
          name: "web_search",
          description: "用于信息检索的网络搜索",
          parameters: {
            type: "object",
            properties: {
              query: {
                type: "string",
                description: "要搜索的内容"
              },
              classes: {
                type: "array",
                description: "搜索领域",
                items: {
                  type: "string",
                  enum: ["all", "academic", "tech"]
                }
              }
            },
            required: ["query"]
          }
        }
      }
    ],
    model: "kimi-latest"
  },

  // 第二步：kimi_chat插件返回（包含tool_calls）
  step2_chat_response: {
    message: "对话成功",
    response: {
      content: null,
      role: "assistant",
      finish_reason: "tool_calls",
      model: "kimi-latest",
      created: 1234567890,
      tool_calls: [
        {
          id: "call_123456",
          type: "function",
          function: {
            name: "web_search",
            arguments: '{"query": "AI技术发展 2024", "classes": ["academic", "tech"]}'
          }
        }
      ]
    },
    status: true,
    usage: { prompt_tokens: 50, completion_tokens: 20, total_tokens: 70 }
  },

  // 第三步：调用kimi_tools插件执行工具
  step3_tool_input: {
    tool_name: "web_search",
    parameters: '{"query": "AI技术发展 2024", "classes": ["academic", "tech"]}',
    call_id: "call_123456"
  },

  // 第四步：kimi_tools插件返回工具执行结果
  step4_tool_response: {
    message: "工具执行成功",
    result: '{"query": "AI技术发展 2024", "results": [...], "success": true}',
    status: true,
    call_id: "call_123456",
    execution_time: 1500
  }
};
