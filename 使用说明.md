# 统计和限流模块使用说明

## 📦 安装依赖

```bash
npm install ioredis
npm install @types/ioredis --save-dev
```

## 🚀 快速开始

### 1. 复制模块文件
将 `stats-rate-limit-module.ts` 复制到你的项目中

### 2. 基本使用

```typescript
import { SimpleStatsService, StatsConfig } from './stats-rate-limit-module';

// 创建服务实例
const statsService = new SimpleStatsService('your-app-name', {
  host: "你的Redis地址",
  port: 6379,
  password: "你的Redis密码",
  db: 0
});

// 配置限流规则
const config: StatsConfig = {
  dailyLimit: 1000,        // 每日最多1000次
  windowMinutes: 60,       // 1小时窗口
  windowLimit: 100         // 1小时内最多100次
};

// 在你的业务函数中使用
async function handleRequest(userId: string, logger?: any) {
  // 检查限流
  const rateLimitResult = await statsService.checkRateLimit(userId, config, logger);
  
  if (!rateLimitResult.allowed) {
    return {
      success: false,
      message: `请求被限制: ${rateLimitResult.reason}`,
      remainingDaily: rateLimitResult.remainingDaily,
      remainingWindow: rateLimitResult.remainingWindow
    };
  }

  // 获取用户统计
  const userStats = await statsService.getUserStats(userId);
  
  // 执行你的业务逻辑
  try {
    const result = await yourBusinessLogic();
    
    return {
      success: true,
      data: result,
      stats: {
        totalCalls: userStats.totalCalls,
        todayCalls: userStats.todayCalls,
        remainingDaily: rateLimitResult.remainingDaily,
        remainingWindow: rateLimitResult.remainingWindow
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '业务处理失败',
      error: error.message
    };
  }
}
```

## 🔧 配置选项

### StatsConfig 接口
```typescript
interface StatsConfig {
  dailyLimit?: number;      // 每日限制次数，不设置表示不限制
  windowMinutes?: number;   // 时间窗口（分钟）
  windowLimit?: number;     // 时间窗口内限制次数
}
```

### 常用配置示例

```typescript
// API 接口限流
const apiConfig: StatsConfig = {
  dailyLimit: 10000,
  windowMinutes: 60,
  windowLimit: 1000
};

// 短信发送限流
const smsConfig: StatsConfig = {
  dailyLimit: 10,
  windowMinutes: 60,
  windowLimit: 5
};

// 文件上传限流
const uploadConfig: StatsConfig = {
  dailyLimit: 100,
  windowMinutes: 10,
  windowLimit: 20
};
```

## 📊 返回数据结构

### RateLimitResult
```typescript
interface RateLimitResult {
  allowed: boolean;         // 是否允许通过
  reason?: string;          // 被限制的原因
  remainingDaily?: number;  // 今日剩余次数
  remainingWindow?: number; // 时间窗口剩余次数
}
```

### UserStats
```typescript
interface UserStats {
  totalCalls: number;       // 总调用次数
  todayCalls: number;       // 今日调用次数
  lastCallTime: Date;       // 最后调用时间
}
```

## 🎯 不同项目的适配

### 1. Express.js 项目
```typescript
import express from 'express';
import { SimpleStatsService } from './stats-rate-limit-module';

const app = express();
const statsService = new SimpleStatsService('express-api');

// 中间件
app.use(async (req, res, next) => {
  const userId = req.user?.id || req.ip;
  const config = { dailyLimit: 1000, windowMinutes: 60, windowLimit: 100 };
  
  const result = await statsService.checkRateLimit(userId, config);
  
  if (!result.allowed) {
    return res.status(429).json({
      error: 'Too Many Requests',
      message: result.reason
    });
  }
  
  req.rateLimit = result;
  next();
});
```

### 2. Koa.js 项目
```typescript
import Koa from 'koa';
import { SimpleStatsService } from './stats-rate-limit-module';

const app = new Koa();
const statsService = new SimpleStatsService('koa-api');

app.use(async (ctx, next) => {
  const userId = ctx.state.user?.id || ctx.ip;
  const config = { dailyLimit: 1000, windowMinutes: 60, windowLimit: 100 };
  
  const result = await statsService.checkRateLimit(userId, config);
  
  if (!result.allowed) {
    ctx.status = 429;
    ctx.body = {
      error: 'Too Many Requests',
      message: result.reason
    };
    return;
  }
  
  ctx.state.rateLimit = result;
  await next();
});
```

### 3. 云函数项目
```typescript
import { SimpleStatsService } from './stats-rate-limit-module';

const statsService = new SimpleStatsService('cloud-function');

exports.handler = async (event, context) => {
  const userId = event.userId || context.requestId;
  const config = { dailyLimit: 500, windowMinutes: 60, windowLimit: 50 };
  
  const result = await statsService.checkRateLimit(userId, config);
  
  if (!result.allowed) {
    return {
      statusCode: 429,
      body: JSON.stringify({
        error: 'Too Many Requests',
        message: result.reason
      })
    };
  }
  
  // 你的业务逻辑
  const response = await handleBusinessLogic(event);
  
  return {
    statusCode: 200,
    body: JSON.stringify(response)
  };
};
```

## ⚠️ 注意事项

1. **Redis 配置**: 确保 Redis 服务可用，配置正确的连接信息
2. **键名前缀**: 不同项目使用不同的 `keyPrefix` 避免冲突
3. **错误处理**: 模块内置了错误处理，Redis 不可用时会允许请求通过
4. **资源清理**: 在应用关闭时调用 `statsService.close()` 关闭 Redis 连接
5. **时区问题**: 统计基于 UTC 时间，如需本地时间请自行调整

## 🔄 版本更新

如需更新功能，只需替换 `stats-rate-limit-module.ts` 文件即可。
