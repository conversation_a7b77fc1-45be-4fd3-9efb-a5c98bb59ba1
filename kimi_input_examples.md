# Kimi AI 插件输入参数格式说明

本文档详细说明了每个Kimi AI插件的输入参数格式和使用示例。

## 1. kimi_chat.ts - 智能聊天插件

### 基础对话
```json
{
  "message": "你好，请介绍一下你自己",
  "system_prompt": "你是一个友善的AI助手",
  "model": "kimi-latest",
  "temperature": 0.3
}
```

### 多轮对话
```json
{
  "message": "那你能帮我做什么呢？",
  "conversation_history": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己"
    },
    {
      "role": "assistant",
      "content": "你好！我是Kimi，一个AI助手..."
    }
  ],
  "model": "kimi-latest"
}
```

### 带工具调用的对话
```json
{
  "message": "请帮我搜索一下人工智能的最新发展",
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "web_search",
        "description": "用于信息检索的网络搜索",
        "parameters": {
          "type": "object",
          "properties": {
            "query": {
              "type": "string",
              "description": "要搜索的内容"
            },
            "classes": {
              "type": "array",
              "description": "搜索领域",
              "items": {
                "type": "string",
                "enum": ["all", "academic", "social", "library", "finance", "code", "ecommerce", "medical"]
              }
            }
          },
          "required": ["query"]
        }
      }
    }
  ],
  "tool_choice": "auto",
  "model": "kimi-latest"
}
```

## 2. kimi_tools.ts - 工具执行插件

### 网络搜索（Kimi官方工具）
```json
{
  "tool_name": "web_search",
  "parameters": "{\"query\": \"人工智能发展趋势\", \"classes\": [\"academic\", \"tech\"]}",
  "call_id": "call_123456"
}
```

### 获取当前时间
```json
{
  "tool_name": "get_current_time",
  "parameters": "{\"timezone\": \"Asia/Shanghai\", \"format\": \"full\"}"
}
```

### 获取天气信息
```json
{
  "tool_name": "get_weather",
  "parameters": "{\"location\": \"北京\", \"unit\": \"celsius\"}"
}
```

### 数学计算
```json
{
  "tool_name": "calculate",
  "parameters": "{\"expression\": \"(25 + 15) * 3 - 8\", \"precision\": 2}"
}
```

### 股票信息查询
```json
{
  "tool_name": "get_stock_info",
  "parameters": "{\"symbol\": \"AAPL\", \"market\": \"US\"}"
}
```

### 货币转换
```json
{
  "tool_name": "currency_convert",
  "parameters": "{\"amount\": 100, \"from_currency\": \"USD\", \"to_currency\": \"CNY\"}"
}
```

### 文本翻译
```json
{
  "tool_name": "translate_text",
  "parameters": "{\"text\": \"Hello, how are you?\", \"from_language\": \"en\", \"to_language\": \"zh\"}"
}
```

### 生成二维码
```json
{
  "tool_name": "generate_qr_code",
  "parameters": "{\"content\": \"https://www.example.com\", \"size\": 200, \"format\": \"PNG\"}"
}
```

### 获取新闻
```json
{
  "tool_name": "get_news",
  "parameters": "{\"category\": \"tech\", \"limit\": 5, \"language\": \"zh\", \"country\": \"cn\"}"
}
```

### 发送通知
```json
{
  "tool_name": "send_notification",
  "parameters": "{\"title\": \"重要提醒\", \"message\": \"您有新的消息\", \"type\": \"info\", \"recipient\": \"user123\"}"
}
```

### 文件操作
```json
{
  "tool_name": "file_operation",
  "parameters": "{\"operation\": \"read\", \"file_path\": \"/path/to/file.txt\"}"
}
```

### 数据分析
```json
{
  "tool_name": "data_analysis",
  "parameters": "{\"data\": [1, 2, 3, 4, 5], \"analysis_type\": \"basic\", \"format\": \"json\"}"
}
```

## 3. kimi_file_analysis.ts - 文件分析插件

### 通过URL分析PDF文档
```json
{
  "file_url": "https://example.com/document.pdf",
  "analysis_prompt": "请总结这个PDF文档的主要内容，并提取其中的关键信息点和重要数据"
}
```

### 分析上传的Excel文件
```json
{
  "file_content": "UEsDBBQAAAAIAA...", 
  "file_name": "sales_data.xlsx",
  "analysis_prompt": "请分析这个Excel文件中的销售数据，包括：1. 总销售额 2. 各产品销售情况 3. 销售趋势分析 4. 提出改进建议"
}
```

### 分析代码文件
```json
{
  "file_content": "ZnVuY3Rpb24gZmlib25hY2NpKG4pIHsKICByZXR1cm4gbiA8PSAxID8gbiA6IGZpYm9uYWNjaShuLTEpICsgZmlib25hY2NpKG4tMik7Cn0=",
  "file_name": "fibonacci.js",
  "analysis_prompt": "请分析这段代码的功能、性能问题，并提供优化建议"
}
```

## 4. kimi_toolkit.ts - 综合工具集插件

### 聊天对话
```json
{
  "action": "chat",
  "content": "请介绍一下人工智能的发展历程",
  "custom_prompt": "你是一个AI技术专家，请用通俗易懂的语言回答",
  "model": "kimi-latest",
  "temperature": 0.3
}
```

### 文档摘要
```json
{
  "action": "summarize",
  "content": "人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支...",
  "model": "moonshot-v1-32k"
}
```

### 文本翻译
```json
{
  "action": "translate",
  "content": "Hello, how are you today? I hope you're having a wonderful day!",
  "target_language": "中文",
  "model": "kimi-latest"
}
```

### 代码分析
```json
{
  "action": "code_analysis",
  "content": "function quickSort(arr) {\n  if (arr.length <= 1) return arr;\n  // ... 代码内容\n}",
  "model": "moonshot-v1-8k"
}
```

### 写作助手
```json
{
  "action": "writing_assistant",
  "content": "我今天去了公园，看到了很多花，很漂亮。天气也很好，我很开心。",
  "writing_type": "日记",
  "model": "kimi-latest",
  "temperature": 0.7
}
```

### 问答提取
```json
{
  "action": "qa_extraction",
  "content": "什么是机器学习？机器学习是人工智能的一个子领域...",
  "model": "moonshot-v1-8k"
}
```

## 参数说明

### 通用参数
- `model`: 模型选择
  - `"kimi-latest"`: 最新Kimi模型（推荐）
  - `"moonshot-v1-8k"`: 8K上下文模型
  - `"moonshot-v1-32k"`: 32K上下文模型
  - `"moonshot-v1-128k"`: 128K上下文模型

- `temperature`: 温度参数（0-2）
  - `0.1-0.3`: 适合事实性任务
  - `0.7-0.9`: 适合创意性任务

- `max_tokens`: 最大输出token数
  - 默认值：1024
  - 范围：1-4096

### 特殊说明
1. **kimi_tools.ts** 的 `parameters` 字段必须是JSON格式的字符串
2. **kimi_file_analysis.ts** 的 `file_content` 需要base64编码
3. **工具调用** 需要先调用kimi_chat.ts获取tool_calls，再调用kimi_tools.ts执行
4. **多轮对话** 需要维护conversation_history数组
