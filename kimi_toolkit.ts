import { Args } from '@/runtime';
import axios from "axios";
import { Input, Output } from "@/typings/kimi_toolkit/kimi_toolkit";

/**
 * 支持的操作类型
 */
type ActionType =
  | 'chat'              // 聊天对话
  | 'summarize'         // 文档摘要
  | 'translate'         // 翻译
  | 'code_analysis'     // 代码分析
  | 'writing_assistant' // 写作助手
  | 'qa_extraction';    // 问答提取

/**
 * 支持的模型类型
 */
type ModelType =
  | 'moonshot-v1-8k'    // 8K上下文模型
  | 'moonshot-v1-32k'   // 32K上下文模型
  | 'moonshot-v1-128k'; // 128K上下文模型



/**
 * Kimi AI 工具集插件
 * 集成了多种Kimi AI功能：文本对话、文档摘要、翻译、代码分析等
 * 提供统一的接口调用不同的AI能力
 *
 * 输入参数示例：
 *
 * 1. 聊天对话：
 * {
 *   "action": "chat",
 *   "content": "请介绍一下人工智能的发展历程",
 *   "custom_prompt": "你是一个AI技术专家，请用通俗易懂的语言回答",
 *   "model": "kimi-latest",
 *   "temperature": 0.3
 * }
 *
 * 2. 文档摘要：
 * {
 *   "action": "summarize",
 *   "content": "人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等...",
 *   "model": "moonshot-v1-32k"
 * }
 *
 * 3. 文本翻译：
 * {
 *   "action": "translate",
 *   "content": "Hello, how are you today? I hope you're having a wonderful day!",
 *   "target_language": "中文",
 *   "model": "kimi-latest"
 * }
 *
 * 4. 代码分析：
 * {
 *   "action": "code_analysis",
 *   "content": "function quickSort(arr) {\n  if (arr.length <= 1) return arr;\n  const pivot = arr[Math.floor(arr.length / 2)];\n  const left = arr.filter(x => x < pivot);\n  const middle = arr.filter(x => x === pivot);\n  const right = arr.filter(x => x > pivot);\n  return [...quickSort(left), ...middle, ...quickSort(right)];\n}",
 *   "model": "moonshot-v1-8k"
 * }
 *
 * 5. 写作助手：
 * {
 *   "action": "writing_assistant",
 *   "content": "我今天去了公园，看到了很多花，很漂亮。天气也很好，我很开心。",
 *   "writing_type": "日记",
 *   "model": "kimi-latest",
 *   "temperature": 0.7
 * }
 *
 * 6. 问答提取：
 * {
 *   "action": "qa_extraction",
 *   "content": "什么是机器学习？机器学习是人工智能的一个子领域，它使计算机系统能够通过经验自动改进性能，而无需明确编程。机器学习有哪些主要类型？主要有三种类型：监督学习、无监督学习和强化学习...",
 *   "model": "moonshot-v1-8k"
 * }
 *
 * 7. 长文档处理：
 * {
 *   "action": "summarize",
 *   "content": "这里是一个很长的文档内容...", // 可以是几万字的长文档
 *   "model": "moonshot-v1-128k", // 使用128k模型处理长文档
 *   "max_tokens": 4096
 * }
 *
 * 8. 创意写作：
 * {
 *   "action": "writing_assistant",
 *   "content": "写一个关于未来城市的科幻故事开头",
 *   "writing_type": "科幻小说",
 *   "model": "kimi-latest",
 *   "temperature": 0.8, // 提高温度以增加创造性
 *   "max_tokens": 2048
 * }
 *
 * 支持的action类型：
 * - "chat": 聊天对话
 * - "summarize": 文档摘要
 * - "translate": 翻译
 * - "code_analysis": 代码分析
 * - "writing_assistant": 写作助手
 * - "qa_extraction": 问答提取
 */
export async function handler({ input, logger }: Args<Input>): Promise<Output> {
  try {
    logger.info("开始处理Kimi工具集请求", {
      action: input.action,
      hasContent: !!input.content,
      model: input.model || 'moonshot-v1-8k'
    });

    // 验证输入参数
    if (!input.action) {
      logger.error("未指定操作类型");
      return {
        message: "请指定要执行的操作类型",
        result: null,
        status: false,
        usage: null
      } as Output;
    }

    if (!input.content || input.content.trim() === '') {
      logger.error("输入内容为空");
      return {
        message: "请提供有效的输入内容",
        result: null,
        status: false,
        usage: null
      } as Output;
    }

    // 根据操作类型执行相应功能
    let result: any;
    switch (input.action) {
      case 'chat':
        result = await performChat(input, logger);
        break;
      case 'summarize':
        result = await performSummarize(input, logger);
        break;
      case 'translate':
        result = await performTranslate(input, logger);
        break;
      case 'code_analysis':
        result = await performCodeAnalysis(input, logger);
        break;
      case 'writing_assistant':
        result = await performWritingAssistant(input, logger);
        break;
      case 'qa_extraction':
        result = await performQAExtraction(input, logger);
        break;
      default:
        logger.error("不支持的操作类型", { action: input.action });
        return {
          message: `不支持的操作类型: ${input.action}`,
          result: null,
          status: false,
          usage: null
        } as Output;
    }

    if (!result.success) {
      return {
        message: result.error || "操作执行失败",
        result: null,
        status: false,
        usage: null
      } as Output;
    }

    logger.info("Kimi工具集操作成功", {
      action: input.action,
      resultLength: result.data?.content?.length || 0,
      usage: result.usage
    });

    return {
      message: "操作执行成功",
      result: {
        action: input.action,
        content: result.data.content,
        model: result.data.model,
        created: result.data.created
      },
      status: true,
      usage: result.usage
    } as Output;

  } catch (error) {
    logger.error("处理Kimi工具集请求时发生异常", error);
    return {
      message: error instanceof Error ? error.message : '未知错误',
      result: null,
      status: false,
      usage: null
    } as Output;
  }
}

/**
 * 执行聊天对话
 */
async function performChat(input: Input, logger: any) {
  const systemPrompt = input.custom_prompt || "你是一个智能助手，能够回答各种问题并提供有用的建议。";
  
  return await callKimiAPI([
    { role: "system", content: systemPrompt },
    { role: "user", content: input.content }
  ], input, logger);
}

/**
 * 执行文档摘要
 */
async function performSummarize(input: Input, logger: any) {
  const systemPrompt = `你是一个专业的文档摘要专家。请对用户提供的内容进行摘要，要求：
1. 提取核心要点和关键信息
2. 保持逻辑清晰，结构合理
3. 摘要长度适中，突出重点
4. 使用简洁明了的语言`;

  const userPrompt = `请对以下内容进行摘要：\n\n${input.content}`;

  return await callKimiAPI([
    { role: "system", content: systemPrompt },
    { role: "user", content: userPrompt }
  ], input, logger);
}

/**
 * 执行翻译
 */
async function performTranslate(input: Input, logger: any) {
  const targetLanguage = input.target_language || "中文";
  const systemPrompt = `你是一个专业的翻译专家，能够准确翻译各种语言。请将用户提供的内容翻译成${targetLanguage}，要求：
1. 保持原文的意思和语调
2. 使用自然流畅的表达
3. 注意专业术语的准确性
4. 保持格式和结构`;

  const userPrompt = `请将以下内容翻译成${targetLanguage}：\n\n${input.content}`;

  return await callKimiAPI([
    { role: "system", content: systemPrompt },
    { role: "user", content: userPrompt }
  ], input, logger);
}

/**
 * 执行代码分析
 */
async function performCodeAnalysis(input: Input, logger: any) {
  const systemPrompt = `你是一个资深的代码审查专家，能够分析各种编程语言的代码。请对用户提供的代码进行分析，包括：
1. 代码功能和逻辑分析
2. 代码质量评估
3. 潜在问题和改进建议
4. 最佳实践建议
5. 性能优化建议`;

  const userPrompt = `请分析以下代码：\n\n${input.content}`;

  return await callKimiAPI([
    { role: "system", content: systemPrompt },
    { role: "user", content: userPrompt }
  ], input, logger);
}

/**
 * 执行写作助手
 */
async function performWritingAssistant(input: Input, logger: any) {
  const writingType = input.writing_type || "通用写作";
  const systemPrompt = `你是一个专业的写作助手，擅长${writingType}。请帮助用户改进和完善文本，包括：
1. 语言表达的优化
2. 逻辑结构的调整
3. 内容的丰富和完善
4. 语法和用词的修正
5. 风格和语调的统一`;

  const userPrompt = `请帮我改进以下${writingType}内容：\n\n${input.content}`;

  return await callKimiAPI([
    { role: "system", content: systemPrompt },
    { role: "user", content: userPrompt }
  ], input, logger);
}

/**
 * 执行问答提取
 */
async function performQAExtraction(input: Input, logger: any) {
  const systemPrompt = `你是一个专业的内容分析专家，能够从文本中提取关键的问答对。请从用户提供的内容中提取重要的问题和答案，要求：
1. 识别文本中的核心问题
2. 提取对应的准确答案
3. 按重要性排序
4. 格式清晰，便于理解`;

  const userPrompt = `请从以下内容中提取重要的问答对：\n\n${input.content}`;

  return await callKimiAPI([
    { role: "system", content: systemPrompt },
    { role: "user", content: userPrompt }
  ], input, logger);
}

/**
 * 调用Kimi API的通用方法
 */
async function callKimiAPI(messages: Array<{role: string, content: string}>, input: Input, logger: any) {
  try {
    const apiKey = "sk-woBMElIQkhFn9xCmZQKeAvln64Y2aIWenKN2Tm6qPOKzjwhr";
    const baseURL = "https://api.moonshot.cn/v1";
    
    const requestData = {
      model: input.model || "moonshot-v1-8k",
      messages: messages,
      temperature: input.temperature || 0.3,
      max_tokens: input.max_tokens || 2048,
      top_p: input.top_p || 1.0,
      stream: false
    };

    logger.info("调用Kimi API", {
      model: requestData.model,
      messagesCount: requestData.messages.length,
      temperature: requestData.temperature
    });

    const response = await axios.post(`${baseURL}/chat/completions`, requestData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 60000
    });

    if (!response.data || !response.data.choices || response.data.choices.length === 0) {
      logger.error("API返回数据格式异常", response.data);
      return { success: false, error: "API返回数据格式异常" };
    }

    return {
      success: true,
      data: {
        content: response.data.choices[0].message.content,
        model: response.data.model,
        created: response.data.created
      },
      usage: response.data.usage
    };

  } catch (error) {
    logger.error("调用Kimi API时发生异常", error);
    
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const errorData = error.response?.data;
      
      switch (status) {
        case 401:
          return { success: false, error: "API密钥无效或已过期" };
        case 429:
          return { success: false, error: "请求频率过高，请稍后重试" };
        case 500:
          return { success: false, error: "Kimi服务器内部错误" };
        default:
          return { 
            success: false, 
            error: errorData?.error?.message || error.message || "API调用失败" 
          };
      }
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : "未知错误"
    };
  }
}
