import { Args } from '@/runtime';
import axios from "axios";
import { Input, Output } from "@/typings/kimi_chat/kimi_chat";

/**
 * 工具调用请求
 */
interface ToolCall {
  /** 调用ID */
  id: string;
  /** 工具类型 */
  type: 'function';
  /** 函数调用信息 */
  function: {
    name: string;
    arguments: string;
  };
}

/**
 * 工具函数定义 - 符合Kimi API标准
 */
interface ToolFunction {
  /** 函数名称 */
  name: string;
  /** 函数描述 */
  description: string;
  /** 函数参数定义 */
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
  };
}

/**
 * 工具定义 - 符合Kimi API标准
 */
interface Tool {
  /** 工具类型，目前只支持function */
  type: 'function';
  /** 函数定义 */
  function: ToolFunction;
}





/**
 * Kimi AI 聊天插件
 * 基于Moonshot AI的Kimi大模型API实现智能对话功能
 * 支持多轮对话、上下文记忆、工具调用等功能
 *
 * 输入参数示例：
 *
 * 1. 基础对话：
 * {
 *   "message": "你好，请介绍一下你自己",
 *   "system_prompt": "你是一个友善的AI助手",
 *   "model": "kimi-latest",
 *   "temperature": 0.3
 * }
 *
 * 2. 多轮对话：
 * {
 *   "message": "那你能帮我做什么呢？",
 *   "conversation_history": [
 *     {
 *       "role": "user",
 *       "content": "你好，请介绍一下你自己"
 *     },
 *     {
 *       "role": "assistant",
 *       "content": "你好！我是Kimi，一个AI助手..."
 *     }
 *   ],
 *   "model": "kimi-latest"
 * }
 *
 * 3. 带工具调用的对话：
 * {
 *   "message": "请帮我搜索一下人工智能的最新发展",
 *   "tools": [
 *     {
 *       "type": "function",
 *       "function": {
 *         "name": "web_search",
 *         "description": "用于信息检索的网络搜索",
 *         "parameters": {
 *           "type": "object",
 *           "properties": {
 *             "query": {
 *               "type": "string",
 *               "description": "要搜索的内容"
 *             },
 *             "classes": {
 *               "type": "array",
 *               "description": "搜索领域",
 *               "items": {
 *                 "type": "string",
 *                 "enum": ["all", "academic", "social", "library", "finance", "code", "ecommerce", "medical"]
 *               }
 *             }
 *           },
 *           "required": ["query"]
 *         }
 *       }
 *     }
 *   ],
 *   "tool_choice": "auto",
 *   "model": "kimi-latest",
 *   "temperature": 0.3,
 *   "max_tokens": 1024
 * }
 *
 * 4. 专业对话（自定义系统提示）：
 * {
 *   "message": "请分析一下React和Vue的优缺点",
 *   "system_prompt": "你是一个资深的前端开发专家，拥有10年以上的开发经验。请用专业但易懂的语言回答问题。",
 *   "model": "moonshot-v1-32k",
 *   "temperature": 0.2,
 *   "max_tokens": 2048
 * }
 */
export async function handler({ input, logger }: Args<Input>): Promise<Output> {
  try {
    logger.info("开始处理Kimi AI聊天请求", {
      message: input.message,
      model: input.model || 'moonshot-v1-8k',
      temperature: input.temperature || 0.3,
      hasHistory: input.conversation_history && input.conversation_history.length > 0,
      hasTools: input.tools && input.tools.length > 0,
      toolChoice: input.tool_choice
    });

    // 验证输入参数
    if (!input.message || input.message.trim() === '') {
      logger.error("输入消息为空");
      return {
        message: "请输入有效的消息内容",
        response: null,
        status: false,
        usage: null
      } as Output;
    }

    // 构建对话消息数组
    const messages = buildMessages(input, logger);

    // 调用Kimi API
    const kimiResponse = await callKimiAPI(messages, input, logger);

    if (!kimiResponse.success) {
      return {
        message: kimiResponse.error || "Kimi API调用失败",
        response: null,
        status: false,
        usage: null
      } as Output;
    }

    const choice = kimiResponse.data.choices[0];
    const message = choice.message;

    logger.info("Kimi AI响应成功", {
      responseLength: message.content?.length || 0,
      finishReason: choice.finish_reason,
      hasToolCalls: message.tool_calls && message.tool_calls.length > 0,
      usage: kimiResponse.data.usage
    });

    return {
      message: "对话成功",
      response: {
        content: message.content,
        role: message.role,
        finish_reason: choice.finish_reason,
        model: kimiResponse.data.model,
        created: kimiResponse.data.created,
        tool_calls: message.tool_calls
      },
      status: true,
      usage: kimiResponse.data.usage
    } as Output;

  } catch (error) {
    logger.error("处理Kimi聊天请求时发生异常", error);
    return {
      message: error instanceof Error ? error.message : '未知错误',
      response: null,
      status: false,
      usage: null
    } as Output;
  }
}

/**
 * 构建对话消息数组
 * @param input 输入参数
 * @param logger 日志记录器
 * @returns 格式化的消息数组
 */
function buildMessages(input: Input, logger: any): Array<{role: string, content: string}> {
  const messages: Array<{role: string, content: string}> = [];
  
  // 添加系统提示词（如果提供）
  if (input.system_prompt && input.system_prompt.trim() !== '') {
    messages.push({
      role: "system",
      content: input.system_prompt.trim()
    });
    logger.info("添加系统提示词", { systemPromptLength: input.system_prompt.length });
  }
  
  // 添加历史对话记录
  if (input.conversation_history && input.conversation_history.length > 0) {
    for (const historyItem of input.conversation_history) {
      if (historyItem.role && historyItem.content) {
        messages.push({
          role: historyItem.role,
          content: historyItem.content
        });
      }
    }
    logger.info("添加历史对话记录", { historyCount: input.conversation_history.length });
  }
  
  // 添加当前用户消息
  messages.push({
    role: "user",
    content: input.message.trim()
  });
  
  logger.info("构建消息数组完成", { totalMessages: messages.length });
  return messages;
}



/**
 * 调用Kimi API进行对话
 * @param messages 消息数组
 * @param input 输入参数
 * @param logger 日志记录器
 * @returns API调用结果
 */
async function callKimiAPI(
  messages: Array<{role: string, content: string | null, tool_calls?: ToolCall[], tool_call_id?: string, name?: string}>,
  input: Input,
  logger: any
): Promise<{success: boolean, data?: any, error?: string}> {
  try {
    const apiKey = "sk-woBMElIQkhFn9xCmZQKeAvln64Y2aIWenKN2Tm6qPOKzjwhr";
    const baseURL = "https://api.moonshot.cn/v1";

    // 构建请求参数
    const requestData: any = {
      model: input.model || "kimi-latest", // 默认使用最新的Kimi模型
      messages: messages,
      temperature: input.temperature || 0.3, // 默认温度0.3，平衡创造性和准确性
      max_tokens: input.max_tokens || 1024, // 默认最大输出1024个token
      top_p: input.top_p || 1.0, // 默认top_p为1.0
      stream: false // 暂不支持流式响应
    };

    // 如果提供了工具，添加到请求中
    if (input.tools && input.tools.length > 0) {
      requestData.tools = input.tools;
      requestData.tool_choice = input.tool_choice || 'auto';
      logger.info("添加工具调用配置", {
        toolsCount: input.tools.length,
        toolChoice: requestData.tool_choice
      });
    }

    logger.info("准备调用Kimi API", {
      model: requestData.model,
      messagesCount: requestData.messages.length,
      temperature: requestData.temperature,
      maxTokens: requestData.max_tokens,
      hasTools: !!requestData.tools
    });

    // 发送API请求
    const response = await axios.post(`${baseURL}/chat/completions`, requestData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 60000 // 60秒超时
    });

    logger.info("Kimi API调用成功", {
      status: response.status,
      hasChoices: response.data?.choices?.length > 0,
      usage: response.data?.usage
    });

    // 验证响应数据
    if (!response.data || !response.data.choices || response.data.choices.length === 0) {
      logger.error("Kimi API返回数据格式异常", response.data);
      return {
        success: false,
        error: "API返回数据格式异常"
      };
    }

    return {
      success: true,
      data: response.data
    };

  } catch (error) {
    logger.error("调用Kimi API时发生异常", error);
    
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const errorData = error.response?.data;
      
      logger.error("Kimi API错误详情", {
        status: status,
        errorData: errorData,
        message: error.message
      });
      
      // 根据HTTP状态码返回具体错误信息
      switch (status) {
        case 401:
          return { success: false, error: "API密钥无效或已过期" };
        case 429:
          return { success: false, error: "请求频率过高，请稍后重试" };
        case 500:
          return { success: false, error: "Kimi服务器内部错误" };
        case 503:
          return { success: false, error: "Kimi服务暂时不可用" };
        default:
          return { 
            success: false, 
            error: errorData?.error?.message || error.message || "API调用失败" 
          };
      }
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : "未知错误"
    };
  }
}





/**
 * 获取预定义的工具列表
 * @returns 工具定义数组
 */
export function getDefaultTools(): Tool[] {
  return [
    {
      type: 'function',
      function: {
        name: 'get_current_time',
        description: '获取当前时间，支持指定时区',
        parameters: {
          type: 'object',
          properties: {
            timezone: {
              type: 'string',
              description: '时区，例如：Asia/Shanghai, America/New_York',
            }
          },
          required: []
        }
      }
    },
    {
      type: 'function',
      function: {
        name: 'get_weather',
        description: '获取指定地点的天气信息',
        parameters: {
          type: 'object',
          properties: {
            location: {
              type: 'string',
              description: '地点名称，例如：北京、上海、纽约',
            }
          },
          required: ['location']
        }
      }
    },
    {
      type: 'function',
      function: {
        name: 'calculate',
        description: '执行数学计算，支持基本的四则运算',
        parameters: {
          type: 'object',
          properties: {
            expression: {
              type: 'string',
              description: '数学表达式，例如：2+3*4, (10-5)/2',
            }
          },
          required: ['expression']
        }
      }
    },
    {
      type: 'function',
      function: {
        name: 'search_web',
        description: '在网络上搜索信息',
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: '搜索关键词',
            },
            limit: {
              type: 'number',
              description: '返回结果数量限制，默认5个',
            }
          },
          required: ['query']
        }
      }
    }
  ];
}
